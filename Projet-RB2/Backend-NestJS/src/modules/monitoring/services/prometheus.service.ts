import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as client from 'prom-client';

/**
 * Options pour l'enregistrement d'une métrique
 */
interface MetricOptions {
  name: string;
  help: string;
  labelNames?: string[];
  buckets?: number[];
}

/**
 * Service pour l'intégration avec Prometheus
 */
@Injectable()
export class PrometheusService implements OnModuleInit {
  private readonly logger = new Logger(PrometheusService.name);
  private readonly registry: client.Registry;
  private readonly counters: Map<string, client.Counter<string>> = new Map();
  private readonly gauges: Map<string, client.Gauge<string>> = new Map();
  private readonly histograms: Map<string, client.Histogram<string>> = new Map();
  private readonly summaries: Map<string, client.Summary<string>> = new Map();
  private readonly enabled: boolean;
  private readonly defaultLabels: Record<string, string>;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('monitoring.prometheus.enabled', true);
    this.registry = new client.Registry();

    // Configurer les labels par défaut
    this.defaultLabels = {
      app: this.configService.get<string>('app.name', 'retreatandbe'),
      environment: this.configService.get<string>('app.environment', 'development'),
    };

    this.logger.log(`PrometheusService initialized with enabled=${this.enabled}`);

    if (this.enabled) {
      // Configurer le registre
      this.registry.setDefaultLabels(this.defaultLabels);

      // Ajouter les métriques par défaut
      client.collectDefaultMetrics({ register: this.registry });
    }
  }

  /**
   * Initialise le service
   */
  onModuleInit() {
    if (!this.enabled) return;

    this.logger.log('PrometheusService initialized');
  }

  /**
   * Récupère le registre Prometheus
   * @returns Registre Prometheus
   */
  getRegistry(): client.Registry {
    return this.registry;
  }

  /**
   * Récupère les métriques au format Prometheus
   * @returns Métriques au format Prometheus
   */
  async getMetrics(): Promise<string> {
    return await this.registry.metrics();
  }

  /**
   * Enregistre un compteur
   * @param options Options du compteur
   * @returns Compteur enregistré
   */
  registerCounter(options: MetricOptions): client.Counter<string> {
    if (!this.enabled) return null;

    try {
      const counter = new client.Counter({
        name: options.name,
        help: options.help,
        labelNames: options.labelNames || [],
      });

      this.registry.registerMetric(counter);
      this.counters.set(options.name, counter);

      return counter;
    } catch (error) {
      this.logger.error(`Error registering counter ${options.name}: ${error.message}`);
      return null;
    }
  }

  /**
   * Enregistre une jauge
   * @param options Options de la jauge
   * @returns Jauge enregistrée
   */
  registerGauge(options: MetricOptions): client.Gauge<string> {
    if (!this.enabled) return null;

    try {
      const gauge = new client.Gauge({
        name: options.name,
        help: options.help,
        labelNames: options.labelNames || [],
      });

      this.registry.registerMetric(gauge);
      this.gauges.set(options.name, gauge);

      return gauge;
    } catch (error) {
      this.logger.error(`Error registering gauge ${options.name}: ${error.message}`);
      return null;
    }
  }

  /**
   * Enregistre un histogramme
   * @param options Options de l'histogramme
   * @returns Histogramme enregistré
   */
  registerHistogram(options: MetricOptions): client.Histogram<string> {
    if (!this.enabled) return null;

    try {
      const histogram = new client.Histogram({
        name: options.name,
        help: options.help,
        labelNames: options.labelNames || [],
        buckets: options.buckets || client.linearBuckets(0.1, 0.1, 10),
      });

      this.registry.registerMetric(histogram);
      this.histograms.set(options.name, histogram);

      return histogram;
    } catch (error) {
      this.logger.error(`Error registering histogram ${options.name}: ${error.message}`);
      return null;
    }
  }

  /**
   * Enregistre un résumé
   * @param options Options du résumé
   * @returns Résumé enregistré
   */
  registerSummary(options: MetricOptions): client.Summary<string> {
    if (!this.enabled) return null;

    try {
      const summary = new client.Summary({
        name: options.name,
        help: options.help,
        labelNames: options.labelNames || [],
      });

      this.registry.registerMetric(summary);
      this.summaries.set(options.name, summary);

      return summary;
    } catch (error) {
      this.logger.error(`Error registering summary ${options.name}: ${error.message}`);
      return null;
    }
  }

  /**
   * Incrémente un compteur
   * @param name Nom du compteur
   * @param labels Labels du compteur
   * @param value Valeur à incrémenter (défaut: 1)
   */
  incrementCounter(name: string, labels?: Record<string, string>, value: number = 1): void {
    if (!this.enabled) return;

    const counter = this.counters.get(name);

    if (counter) {
      if (labels) {
        counter.inc(labels, value);
      } else {
        counter.inc(value);
      }
    } else {
      this.logger.warn(`Counter ${name} not found`);
    }
  }

  /**
   * Définit la valeur d'une jauge
   * @param name Nom de la jauge
   * @param value Valeur à définir
   * @param labels Labels de la jauge
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    if (!this.enabled) return;

    const gauge = this.gauges.get(name);

    if (gauge) {
      if (labels) {
        gauge.set(labels, value);
      } else {
        gauge.set(value);
      }
    } else {
      this.logger.warn(`Gauge ${name} not found`);
    }
  }

  /**
   * Incrémente une jauge
   * @param name Nom de la jauge
   * @param value Valeur à incrémenter (défaut: 1)
   * @param labels Labels de la jauge
   */
  incrementGauge(name: string, value: number = 1, labels?: Record<string, string>): void {
    if (!this.enabled) return;

    const gauge = this.gauges.get(name);

    if (gauge) {
      if (labels) {
        gauge.inc(labels, value);
      } else {
        gauge.inc(value);
      }
    } else {
      this.logger.warn(`Gauge ${name} not found`);
    }
  }

  /**
   * Décrémente une jauge
   * @param name Nom de la jauge
   * @param value Valeur à décrémenter (défaut: 1)
   * @param labels Labels de la jauge
   */
  decrementGauge(name: string, value: number = 1, labels?: Record<string, string>): void {
    if (!this.enabled) return;

    const gauge = this.gauges.get(name);

    if (gauge) {
      if (labels) {
        gauge.dec(labels, value);
      } else {
        gauge.dec(value);
      }
    } else {
      this.logger.warn(`Gauge ${name} not found`);
    }
  }

  /**
   * Observe une valeur dans un histogramme
   * @param name Nom de l'histogramme
   * @param value Valeur à observer
   * @param labels Labels de l'histogramme
   */
  observeHistogram(name: string, value: number, labels?: Record<string, string>): void {
    if (!this.enabled) return;

    const histogram = this.histograms.get(name);

    if (histogram) {
      if (labels) {
        histogram.observe(labels, value);
      } else {
        histogram.observe(value);
      }
    } else {
      this.logger.warn(`Histogram ${name} not found`);
    }
  }

  /**
   * Observe une valeur dans un résumé
   * @param name Nom du résumé
   * @param value Valeur à observer
   * @param labels Labels du résumé
   */
  observeSummary(name: string, value: number, labels?: Record<string, string>): void {
    if (!this.enabled) return;

    const summary = this.summaries.get(name);

    if (summary) {
      if (labels) {
        summary.observe(labels, value);
      } else {
        summary.observe(value);
      }
    } else {
      this.logger.warn(`Summary ${name} not found`);
    }
  }

  /**
   * Récupère la valeur d'un compteur
   * @param name Nom du compteur
   * @param labels Labels du compteur
   * @returns Valeur du compteur
   */
  getCounter(name: string, labels?: Record<string, string>): number {
    if (!this.enabled) return 0;

    const counter = this.counters.get(name);

    if (counter) {
      const counterData = await counter.get();
      return counterData.values.find(value => {
        if (!labels) return true;

        // Vérifier si tous les labels correspondent
        for (const [key, val] of Object.entries(labels)) {
          if (value.labels[key] !== val) {
            return false;
          }
        }

        return true;
      })?.value || 0;
    }

    return 0;
  }

  /**
   * Récupère la valeur d'une jauge
   * @param name Nom de la jauge
   * @param labels Labels de la jauge
   * @returns Valeur de la jauge
   */
  getGaugeValue(name: string, labels?: Record<string, string>): number {
    if (!this.enabled) return 0;

    const gauge = this.gauges.get(name);

    if (gauge) {
      const gaugeData = await gauge.get();
      return gaugeData.values.find(value => {
        if (!labels) return true;

        // Vérifier si tous les labels correspondent
        for (const [key, val] of Object.entries(labels)) {
          if (value.labels[key] !== val) {
            return false;
          }
        }

        return true;
      })?.value || 0;
    }

    return 0;
  }
}
